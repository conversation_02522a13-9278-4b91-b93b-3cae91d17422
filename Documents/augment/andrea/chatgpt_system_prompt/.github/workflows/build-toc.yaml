name: Generate TOC on PR Merge or Push

# This workflow automatically updates the TOC.md files in the repository
# whenever there's a PR merge or direct push to the main branch.
# It ensures that the table of contents stays up-to-date without manual intervention.

on:
  # Trigger on PR merge
  pull_request_target:
    types:
      - closed
    branches:
      - main
  # Trigger on direct pushes to main branch
  push:
    branches:
      - main

jobs:
  build_toc:
    # Only run on PR merge or direct push to main
    if: github.event_name == 'push' || (github.event_name == 'pull_request_target' && github.event.pull_request.merged == true)
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          # Fetch all history for allowing proper git operations
          fetch-depth: 0

      - name: Set up Python 3
        uses: actions/setup-python@v3
        with:
          python-version: '3.x'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          if [ -f .scripts/requirements.txt ]; then pip install -r .scripts/requirements.txt; fi

      - name: Run TOC generation script
        run: |
          chmod +x .scripts/idxtool.py
          python3 .scripts/idxtool.py --toc

      - name: Commit TOC updates
        run: |
          git config --global user.name 'LouisShark'
          git config --global user.email '<EMAIL>'
          git add TOC.md prompts/*/TOC.md
          git commit -m "docs: Update TOC.md files" || echo "No changes to commit"
          git pull --rebase origin main
          git push origin HEAD:main
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}