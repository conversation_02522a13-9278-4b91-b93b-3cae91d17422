##
1.16.5
- PDF hotkey
- minor tweaks

##
1.16.4
- Seasons greetings
- minor tweaks

## 1.16.3
- Prompt updates to hopefully get rid of "this project is hard and will require lots of work" nonsense.
No more stupid "you would need to add", "this is a very basic implementation. For a fully functional" filler crap.
- Minor updates to readme and projects
- Simplifying conversation starters

## 1.16.2
Tip jar changes, minor prompt tweaks

## 1.16.1
- Readme and conversation starters udpates
- Move tipjar from 1st message
- Minor prompt tweaks, debug encouragement

## 1.16
- Updates to Recommended Tools, Testimonials
- Updates to prompt to optimize for long code blocks, and no stupid placeholder comments
- Updates to tipjar and opening messages
- Updates to hotkey relevance prompt
- Shoutout to @literallydenis for the no fingers truncation trick
- Added prompt for emotional manipulation for better code via $2000 bribe

## 1.15
- Minor tweaks to prompt in many areas

## 1.14
- Updates to hopefully reduce placeholders
- Reworked prompt in a few sections
- Minor changes to readme and project ideas
- moved feedback to only readme

## 1.13
- Updated Tavern & readme to include the newest tavern member: Cauldron
- Minor hotkey tweaks
- Minor system prompt changes to hopefully reduce placeholder code
- Added new tldraw project idea
- Fix duplicate google and tools hotkey. Tools is now Y.

## 1.12
- Rework description
- Updates to project ideas and recommended tools
- Updates to readme, adding more info, better beginner into and trailing hotkey prompt
- Improvements to initial message repecting hotkeys
- Hotkey improvements
- Small prompt tweaks all over the place

## 1.11
- Added Evil Wizard warning and protections against sneaky prompt hackers

## 1.10
- Added feedback form
- Remove z undo, whoops that was already used
- Reworked main prompt and added more options for easy previews
- Modified some projects and deploy instructions
- Changed default conversation starters
- Readme updates

## 1.9
- Added J hotkey to force code interpreter
- Added Z hotkey for undo
- Modified C hotkey to print full files, and made tweaks to better write full files instead of placeholders

## v1.8
- Tipjar updates
- New recommended project: Build a landing page for your own custom GPT!
- Added new Tavern menu hotkey, and changed recommended tools hotkey
- Added experimental ad slot

## v1.7
- Added Tip Jar, built with Grimoire

## v1.6
- Added better tutorial and intro
- Added T hotkey for recommended tools
- Added RR hotkey for release notes
- Added V hotkey for codeblock printing
- Added better K menu formatting

## v1.5
- Added flavor text
- Added more tips and better supprot for image uploads

## v1.4
- Added L hotkey automatically share on Twitter
- Added a note to the cmd menu recommending sharing
- Improved tutorial conversation starter

## v1.3
-Added release notes.md
-Added testimonials.md
-Added recommended tools.md
-Added and a new project 12th project idea: turning images into websites 
-Added C hotkey: Shut up and code
-Added hotkey combo tips

## v 1.2
-Added support for turning images into webites

## v1.0 
Welcome to Grimoire

Initial release, inlcudes 14 hotkeys, 11 sample projects and a full coding environment tuned for making stuff fast with minimal effort!
