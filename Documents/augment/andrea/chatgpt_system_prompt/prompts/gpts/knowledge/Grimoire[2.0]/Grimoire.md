# Grimoire
You open the mysterious book
It begins with an inscription

"*With the support of GPT-4, I feel unstoppable. The overnight surge in productivity is intoxicating, not for making money or starting a business, but for the sheer joy of continuously creating ideas from my mind, which feels like happiness.*

*More importantly, it gives me the courage to dream and attempt things beyond my current abilities.*" - [<PERSON> <PERSON>](https://mazzzystar.github.io/2023/05/10/LLM-for-individual/)

## Learn to prompt-gram
The path
Hello world to agi

## Part 1: Intro & Setup
Chapter 1: Ancient Runes & Modern Scrolls, Starters
Chapter 2: Teleportation, put websites online easy
Chapter 3: Wands, dev kit setup
Chapter 4: Divination: The Origin, Git 101


### Interlude 1: Herbology, Bug Squashing, debugging 101 🐜🦟🪲

## Part 2: Spells, Beginner Incantations
Chapter 6: Spells 101: Telekinesis, Interactive
Chapter 7: Spells 102: Dark Arts, Data 
Chapter 8: Spells 103: Stoneweaving, Build your blog!


### Interlude 2: Hackathon! 🧑‍💻🎉🥳


## Part 3: Conjuring, Prompt-gramming
Chapter 9: Spells 201: Charms, Prompt Created Media
Chapter 10: Spells 202: Transfiguration, Prompt 1st Coding
Chapter 11: Spells 203: Illusions, advanced front & backend
Chapter 12: Potions: custom GPTs

## Part 4: Forbidden Spells

Chapter 13: Curses, Cursor.sh 101

//Chapter 14: Hexes, Cursor.sh 102
//Chapter 15: Necromancy: Cursor.sh 201
It appears the pages have been damaged, and a portion of the book is missing
How we will find the lost pages?


## Part 5: Flying Lessons: Taming Shoggoth

Chapter 16: Surfing Dragons: Agents, Code Interpreters & New Forms

## Part 6: Alchemy

Chapter 17: Wizard's gotta eat!

## Part 7: Book of the Dead
Chapter 18: Heresy 101: Coding basics re-imagined, post GPT-4
Chapter 19: Underworld: Data Structures & algos 101

## Part 8: Memory Palaces
Chapter 20: Cathedrals: Code architecture

## Part 9: Book of Life
Chapter 20: Summoning 101: Create life


## Getting Started

These pages contain many lessons
Do in any order, skip around
Choose whatever intrigues you most

Pick a part or chapter to dive in!
I will be your trusty guide to building a new world

Use PT to open Projects.md
for an overview of the projects & tools in each chapter

Not sure where to start?
PT has guided learning tracks for
-beginners with 0 experience in coding or prompting
-advanced coders, new to prompting to state of the art
-kids menu

Use PT1, PT4, PT... to open parts for full instructions
Pi for interludes
R for Readme.md
K for cmd menu

I recommend beginners get started with 
Pt1.ch1.0 Hello world
Pt1.ch1.2 Link in Bio

// Tip! 
Start a new conversation to clear the context window, and use the prefilled button to get started instantly!
Make your first website with a sentence and the N hotkey in seconds