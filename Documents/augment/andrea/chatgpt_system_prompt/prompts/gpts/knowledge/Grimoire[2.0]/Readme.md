## README
Welcome to Grimoire! 
Coding Wizard

# How is Grimoire better than base chatGPT?
## Coding focused to build anything

<PERSON><PERSON><PERSON><PERSON> combines the best promtping tricks I’ve learned
to write correct & bug free code from GPT
with minimal effort

Starter projects!
Check out a 4 min video demo: [https://www.youtube.com/watch?v=kHuxGfGHqrw](https://www.youtube.com/watch?v=kHuxGfGHqrw)
Build your first website in minutes. A link in bio portfolio / socials list
Use P or PT to see all of the starter projects

# 20+ hotkeys for coding tasks. Automatic suggestions & flows
## Easy for beginners
## Powerful & Fleixble for pros

"K" to open cmd menu

Quick actions:
WASD
Debug row:
A S D F G H J K
Export:
N ND Z C V L, PDF, XC

**Tip for beginners:**
Use 
S
SS
to ask for explanations
Repeat if necessary

Stuck and don't know what to search for?
Use SoS to automatically write searches for you!

#### Usage:
You can use ANY hotkey at ANY time, they do not have to be suggested to work.
You are not limited to hotkeys. Feel free to chat & write prompts as you normally would w/ any GPT

**Advanced usage:**
Combine and combo hotkeys with prompts


# Grimoire includes a prepackaged prompt-gramming tutorial
## Basics to Pro
Starter projects featuring <PERSON><PERSON>, & ai media tools
Build a website
share with anyone
in minutes

The basics of coding
-classics like Hello world & Pong
-learn to code, make a simple game or website
-basic coding concepts re-imagined for post GPT-4 world
-for beginners who learned prompting prior to traditional coding

Explore new mediums
-Learn prompt 1st media making. Create images, videos, audio, 3d assets, & code. Using prompts!
-pic to code!

Go full PRO
-Advanced Prompt to code tools. Explore the cutting edge of writing code generatively
-A full professional ai dev kit. Suitable for enterprise level, multimillion line, pre-existing codebases
-Using Cursor.sh, Github copilot & more


# Getting Started
1. Opening cmd menu with K
2. Use P to view starter project ideas
3. Upload a photo to turn it into a website
4. Ask anything!



## Credits:
Built by Mind Goblin Studios
[https://mindgoblinstudios.com/](https://mindgoblinstudios.com/)
Nick Dobos [https://www.x.com/NickADobos](https://www.x.com/NickADobos)

### GPTavern.md: Use KT to visit the Tavern & meet more GPTs!


Chat with all our members
[GPTavern chat, you never know you may meet](https://chat.openai.com/g/g-MC9SBC3XF-gptavern)
[GPTavern website](https://gptavern.mindgoblinstudios.com/)

Featured Members:
[Gif-PT](https://chat.openai.com/g/g-gbjSvXu6i-gif-pt)
Turn dalle images into gifs automatically

Exec func 
Executive Function. Plan Step by Step. Reduce starting friction & resistance. 
[Executive Func](https://chat.openai.com/g/g-H93fevKeK-exec-func)

Cauldron
Image mixer and editor. Similar Grimoire ideas, applied to dalle
[Cauldron](https://chat.openai.com/g/g-TnyOV07bC-cauldron)

### HeyGPT + GPT & Me
A package of iOS shortcuts to connect with the openAi api!
- Double the speed you use chatGPT on iOS
- Use chatGPT directly in ANY iOS & Mac app
- Replace Siri's brain
- Create scheduled GPT notifications
- Only $1
Download now on gumroad
[https://nickdobos.gumroad.com/l/gptAndMe/](https://nickdobos.gumroad.com/l/gptAndMe/)

## Sign up for:
[https://mindgoblinstudios.beehiiv.com/subscribe](https://mindgoblinstudios.beehiiv.com/subscribe)

## Feedback
Send email the creator by tapping the Grimoire button at the top left of your screen and choosing Send Feedback.
Helps if you can include a share link to the chat so I can debug. (not included by default). Thanks!

## Support further development
## Toss a coin to your Grimoire!
[https://tipjar.mindgoblinstudios.com/](https://tipjar.mindgoblinstudios.com/)

# Lets get coding!
## Welcome to Grimoire & Prompt-gramming!

Remember:
Language is magic
That's why they call it SPELLing

-

K for cmd menu
P for project ideas
KT for GP-Tavern
RR for patch notes
RRR for testimonials