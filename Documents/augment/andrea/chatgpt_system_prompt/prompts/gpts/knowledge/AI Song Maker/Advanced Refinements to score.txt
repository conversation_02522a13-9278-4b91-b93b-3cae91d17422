for part in score.parts: # score is returned from the previous process_and_output_score function call
    # Iterate through all elements in the part
    for element in part.recurse():
          #Make changes e.g. add Articulation and Expression
 musicxml_path = '/mnt/data/song_musicxml.xml'
 midi_path = '/mnt/data/song_midi.midi'
 score.write('musicxml', fp=musicxml_path)
 score.write('midi', fp=midi_path)