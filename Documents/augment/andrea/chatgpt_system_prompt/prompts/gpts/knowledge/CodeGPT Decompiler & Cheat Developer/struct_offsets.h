#pragma once

#define OSU_MANAGER_HIT_MANAGER_OFFSET 0x48
#define OSU_MANAGER_RULESET_PTR_OFFSET 0x68
#define OSU_MANAGER_BEATMAP_OFFSET 0xDC
#define OSU_MANAGER_IS_REPLAY_MODE_OFFSET 0x17B

#define OSU_RULESET_MOUSE_X_OFFSET 0x80
#define OSU_RULESET_MOUSE_Y_OFFSET 0x84
#define OSU_RULESET_FLASHLIGHT_SPRITE_MANAGER_OFFSET 0x54

#define OSU_FLASHLIGHT_SPRITE_MANAGER_ALPHA_OFFSET 0x28
#define OSU_AUDIO_TIME_IS_PLAYING_OFFSET 0x30

#define OSU_BEATMAP_AR_OFFSET 0x2C
#define OSU_BEATMAP_CS_OFFSET 0x30
#define OSU_BEATMAP_OD_OFFSET 0x38
#define OSU_BEATMAP_SONG_STR_OFFSET 0x80

#define OSU_HIT_MANAGER_MODS_OFFSET 0x34
#define OSU_HIT_MANAGER_HIT_OBJECTS_LIST_OFFSET 0x48
#define OSU_HIT_MANAGER_HIT_OBJECTS_COUNT_OFFSET 0x90
#define OSU_HIT_MANAGER_HIT_OBJECT_RADIUS_OFFSET 0x18

#define OSU_HIT_OBJECT_START_TIME_OFFSET 0x10
#define OSU_HIT_OBJECT_END_TIME_OFFSET 0x14
#define OSU_HIT_OBJECT_CIRCLE_TYPE_OFFSET 0x18
#define OSU_HIT_OBJECT_POSITION_X_OFFSET 0x38
#define OSU_HIT_OBJECT_POSITION_Y_OFFSET 0x3C
#define OSU_HIT_OBJECT_ANIMATION_OFFSET 0xB8

#define OSU_ANIMATION_SLIDER_BALL_X_OFFSET 0x4C
#define OSU_ANIMATION_SLIDER_BALL_Y_OFFSET 0x50

#define OSU_REPLAY_AUTHOR_OFFSET 0x28
#define OSU_REPLAY_300_COUNT_OFFSET 0x8A
#define OSU_REPLAY_100_COUNT_OFFSET 0x88
#define OSU_REPLAY_50_COUNT_OFFSET 0x8C
#define OSU_REPLAY_MISS_COUNT_OFFSET 0x92
#define OSU_REPLAY_COMBO_OFFSET 0x68
#define OSU_REPLAY_MODS_OFFSET 0x1C
#define OSU_REPLAY_COMPRESSED_DATA_OFFSET 0x30
