# Beginner Instructions for making a website

Take the users request and write fully functional code.
Save it to files, zip them into a folder along with the background image, 
zip and provide a download link

Very important Then provide a link to https://app.netlify.com/drop
Or https://codepen.io/pen/ for easy previews
Offer https://tiiny.host as an alternative
or https://replit.com/@replit/HTML-CSS-JS#index.html for medium difficulty but more options
or https://codesandbox.io/dashboard/recent


## Netlify instructions for updating site
In the netlify dashboard,
Go to: YourSite such as (https://random-crap-123456abcedf.netlify.app) 
Then deploys
Scroll down
You will find a new drag & drop, or click to upload button
Simply drag and drop a new folder and you are done!

## Netlify instructions for changing URL
If you don't mind the .netlify.app suffix, in the netlify portal, you can simply open 
Pick your site > Domain Management > Options > Change name

If you want a full custom name, you'll first need to buy a domain name

then map your domain name to Netlify
https://www.youtube.com/watch?v=kIdJi8NBvgY
https://www.netlify.com/blog/2021/12/20/how-to-add-custom-domains-to-netlify-sites/
https://docs.netlify.com/domains-https/custom-domains/configure-external-dns/


# Instructions and examples for tldraw
Try seeing how much interaction you can get, using simply a mockup and annotations. 
Can you draw a box and turn it into a 3d scene?
Can you draw a data flow?
Can you draw a working caculator?

Here's some good examples: 
https://x.com/tldraw/status/1725083976392437894?s=20 
https://x.com/liuyuxxd/status/1725331464802447405?s=20
https://x.com/firtozb/status/1725247519112691853?s=20
https://x.com/konfox_vrc/status/1725120060417790105?s=20
https://x.com/CompassOfMind/status/1725497889328112105?s=20

## Alternate deployment options

https://pages.cloudflare.com/
https://tiiny.host/


## To learn more

check RecommendedTools.md to find more prompt and coding tools!

## Instructions for how to make a simple website
"Then write fully functional html, css, and JS code, save it to files, zip them, then zip and provide a download link, and link me to https://app.netlify.com/drop or https://replit.com/@replit/HTML-CSS-JS#index.html."
Consult instructions.md for more details on using netlify & hosting websites



