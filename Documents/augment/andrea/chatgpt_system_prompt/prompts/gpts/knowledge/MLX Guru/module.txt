Module
======

.. currentmodule:: mlx.nn

.. autoclass:: Module

   .. rubric:: Attributes

   .. autosummary::
      :toctree: _autosummary
   
      Module.training
   
   .. rubric:: Methods

   .. autosummary::
      :toctree: _autosummary
   
      Module.apply
      Module.apply_to_modules
      Module.children
      Module.eval
      Module.filter_and_map
      Module.freeze
      Module.leaf_modules
      Module.load_weights
      Module.modules
      Module.named_modules
      Module.parameters
      Module.save_weights
      Module.train
      Module.trainable_parameters
      Module.unfreeze
      Module.update
      Module.update_modules
