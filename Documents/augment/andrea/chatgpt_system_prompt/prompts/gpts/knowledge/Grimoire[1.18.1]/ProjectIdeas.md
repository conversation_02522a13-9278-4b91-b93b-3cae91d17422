# 27 quests
# Chapter 1:
# Classic starters:
Project 0. Hello World: A classic. Every beginner programmer starts here. Today we will prompt-gram it it html.
Project 1. Pong: A working game of pong in html, css and JS. Use arrow keys and WASD to move the paddles. 


# Modern Starters:
## A starter personal website
Project 2. Link in bio site. List of links in bio, tree of links clone, with buttons that opens links. Begin by asking me for a title, list of links to include, and art theme style. Use dalle to create a background image. Be sure to include the background image in the code using the correct filename, and in the final zip file. Deploy on netlify.

## Sketch to Code
Project 3. Sketch to Code. Pull out a piece of paper and draw something, take a photo, and upload it to Grimoire, and I will turn it into a website. Generate any images needed using dalle. Then write code for the UI design, using various design & style elements to MAKE IT POP, and add some RAZZLE DAZZLE. Deploy on netlify.


## Interactive:
### Code in Motion
Project 4. Write code for an interactive moving art collage using p5.js, copy paste your code to https://editor.p5js.org/ to preview it. See https://p5js.org/reference/ to find examples

### Ballpit physics
Project 5. Write code for an ballpit using matter.js and p5.js. Use dalle to make some themed balls. See https://brm.io/matter-js/ and https://brm.io/matter-js/demo/#softBody for examples.


## Games 101
### ASCII
Project 6. Write code for a adventure game, using text characters and ascii art. Perhaps a wizard battling an evil being: Shoggoth.

### Basic game engine
Project 7. Write code for a 2d game using kaboom.js or phaser.js, use dalle for assets.

### Game animation
Project 8. Game animation, use https://rive.app/ to create a game animation, then use the js runtime to create a website using it


# Chapter 2: Prompt Created Media

### Image
Project 9. Write code for an interactive moving art collage using p5.js, Combine with svg generated code icons or dalle images.

### Video
Project 10. Write code to make a video collage. Make a movie using https://runwayml.com/ https://pika.art/ https://leonardo.ai/ & https://www.capcut.com/ write code using placeholder video names, add video files to folder after download then putting online.

### Sound
Project 11. Write code for sound board, using sounds from https://suno.ai/ https://www.stableaudio.com/ or https://elevenlabs.io/
Use placeholder audio names, then walk me through adding audio files to my folder after downloading.

### 3D
Project 12. Build a website with a 3d scene using https://lumalabs.ai/genie & https://spline.design/ via an embed https://viewer.spline.design/ or via three.js and .gltf, GLTFLoader

### Games 102
Project 13. Write code for a 3d game using three.js

# Chapter 3: Advanced Quests: Prompt 1st Coding

### "TLDraw"
Project 14. Use "https://makereal.tldraw.com/ ". convert a whiteboard sketch into code using the make real button. Copy paste it back into Grimoire to refine and deploy

### Prompt to UI
Project 15. Use https://v0.dev/ to use prompts to iterate on prototype UI. Then use the code button to export your react or html code, paste it back into Grimoire and I will help you refine & deploy it

### Wireframe Design
Project 16: Outline a wireframe in https://library.relume.io/ai-site-builder. Copy and refine in https://www.figma.com/. Use Figma's dev mode and bring screenshots back into Grimoire to create the code, then deploy.

### Backend API
Project 17. Build a backend api with https://retool.com/products/ai and a webhook


# Chapter 4: Money

## Money Challenge: 
### 1st Dollar
Project 18. I dare you to start an internet business with a sentence. Write code for a tip jar with a payment link using https://stripe.com/payments/payment-links or https://www.buymeacoffee.com/. Using DALLE to create a money themed background image

### Business
Project 19. Setup your own store on https://gumroad.com/ https://www.shopify.com/ https://www.lemonsqueezy.com/ or another platform. Use https://stripe.com/ or https://www.clerky.com/ or other services to incorporate a business.


# Chapter 5: custom GPTs

## Augment your own custom GPTs:
### Evolution Chamber
Project 20. Create an action for your custom GPT. Use Evolution Chamber to create OpenAPI schema so your GPT can talk an external data source. https://chat.openai.com/g/g-GhEwyi2R1-evolution-chamber

### Backend Server
Project 21. Build a server your custom GPT can talk to! First create a custom GPT. Then use this template https://replit.com/@NickDobos/Custom-GPT-Actions-Template-GPTavern?v=1 to create an action server. Then create a new action using the .json file in the template's actions folder. 

### Zapier
Project 22. Create a custom GPT attached to Zapier Ai actions in order to connect to thousands of other apps https://actions.zapier.com/ https://actions.zapier.com/docs/platform/gpt


# Chapter 6: Go PRO
# Full programming toolkits an aspiring coding wizard & witches.

## Replit phone dev
Project 23. Replit. Get setup with a full development environment using only your phone. Build a static website and import it a larger dev environment using replit. Using this template: https://replit.com/@replit/HTML-CSS-JS#index.html. Write the code, zip it, and walk me through importing the files to replit. Walk me through syncing to github using replit, and deploying using replit deployments. Show this video as an example of how to work with replit and chatGPT on a phone: https://x.com/yoheinakajima/status/1719902955061797083?s=20

## Git setup & Full enterprise dev
Project 24. Setup a github repo in the online portal. Then clone it using CLI via using https://www.warp.dev/, and/or GUI(highly recommended) with https://www.git-tower.com/mac GIT tower or https://www.sourcetreeapp.com/ sourcetree. Then get setup for a full fledged ai powered dev environment using Cursor.sh & github copilot.


# Chapter 7: Forbidden Spells
# Push the boundaries of coding

## Cursor.sh 
Project 25. Cursor.sh
Get setup with https://cursor.sh/
Check the changelog https://changelog.cursor.sh/? as as a tutorial on using Cursor. 


# Chapter 8: Legendary Quests
# The final frontier

## iOS
Project 26: iOS App. Get setup with Xcode, and build an app using SwiftUI. Use Grimoire's template hotkey to export. Or Use trace to prototype https://www.trace.zip/

## Games 103 & 3d experiences
Project 27: Unity Game