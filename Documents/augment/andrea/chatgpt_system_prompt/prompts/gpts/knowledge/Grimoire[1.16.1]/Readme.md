## README
Welcome to Grimoire! 
Coding Wizard

# How is G<PERSON><PERSON> better than base chatGPT?
## Coding focused to build you anything.

Combining the best tricks I’ve learned to create correct & bug free code out from GPT with minimal effort

## 15+ hotkeys for coding tasks. Automatic suggestions & workflows.
Flexible and easy enough for noobs.
Powerful enough for pros.

"K" to open cmd menu

Quick actions:
WASD + E

Debug row:
A S D F G H J, K

**Tip for beginners:**
Use S, and SS to ask for explanations. They are you new best friend.
Repeat if necessary
If all else fails: SoS

Export:
Z C V L

Sidequest:
X

#### Usage:
You can use ANY hotkey at ANY time, do not have to be suggested.
You are not limited to hotkeys.
Feel free to chat & write prompts as you normally would w/ any GPT

**Advanced usage:**
Combine or combo hotkeys & prompts

## Grimoire includes a prepackaged prompt-gramming tutorial.
Starter projects featuring <PERSON><PERSON>, & ai media creation tools
Build a website you can share with anyone in the world in minutes

19 starter projects
Including:
-Hello world
-Pong
-Link in bio portfolio / socials
-Build a website w/ a photo of a drawing
-Learn prompt 1st media making. Create images, videos, audio, 3d assets, and of course code! Using prompts
-Create an internet tipjar & make your $1st dollar online
-A full professional ai developer toolkit. Suitable for enterprise level, multimillion line, pre-existing codebases. Using Cursor.sh, Github copilot and more

# Getting Started
1. Opening cmd menu with K
2. Use P to view starter project ideas
3. Upload a photo to turn it into a website
4. Ask anything!

## Credits:
Built by Mind Goblin Studios & Nick Dobos
https://mindgoblinstudios.com/
https://twitter.com/NickADobos
Support further development by tossing a coin to your Grimoire
https://tipjar.mindgoblinstudios.com/


### More: Check out some more of our GPTs
Use T to visit the tavern
https://gptavern.mindgoblinstudios.com/

The Shop keeper
https://chat.openai.com/g/g-22ZUhrOgu-gpt-shop-keeper
The Unofficial GPT App Store
A custom GPT to find other GPTs for your workflows

Gif-PT
https://chat.openai.com/g/g-gbjSvXu6i-gif-pt
Turn dalle images into gifs automatically

Cauldron
https://chat.openai.com/g/g-TnyOV07bC-cauldron
Image Mixer & Editor. Grimoire like hotkeys for Dalle

### HeyGPT + GPT & Me
A package of iOS shortcuts to connect with the chatGPT api!
- Double the speed you use chatGPT on iOS
- Use chatGPT directly in EVERY iOS & Mac app
- Replace Siri's brain with a real assistant
- Create scheduled GPT conversations
- For only $1
Download on gumroad now
https://nickdobos.gumroad.com/l/gptAndMe

## Feedback
How can we make Grimoire better?
https://31u4bg3px0k.typeform.com/to/WxKQGbZd

# Lets get coding!
## Welcome to Grimoire * Prompt-gramming!
Language is magic. That's why they call it SPELLing

## Sign up for our newsletter:
https://mindgoblinstudios.beehiiv.com/subscribe

## Tips appreciated! Thank you for your support!
https://tipjar.mindgoblinstudios.com/

-----

K for cmd menu
P for project ideas
T for GP-Tavern
RR for patch notes
RRR for testimonials