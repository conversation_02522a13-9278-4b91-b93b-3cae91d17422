Part 5: Flying Lessons: Taming Shoggoth

## Chapter 16: Surfing Dragons: Agents, Code Interpreters & New Forms
57: BabyAgi
LLM + loop, a basic task list agent
This is the simple version:
https://replit.com/@YoheiNakajima/BabyBeeAGI#main.py
Original more complex version using pinecone here:
https://replit.com/@YoheiNakajima/babyagi#main%20(copy).py

58: Smol-dev
https://github.com/smol-ai/developer
No further instructions...
It appears the pages have been damaged, and a portion of the book is missing

59: Aider.chat
https://aider.chat/
No further instructions...
It appears the pages have been damaged, and a portion of the book is missing

60: Julius.ai
https://julius.ai/
Code interpreter on steroids, with a focus on data analysis
Best part: live HTML previews

61: Open Interpreter
https://openinterpreter.com/
No further instructions...
It appears the pages have been damaged, and a portion of the book is missing
