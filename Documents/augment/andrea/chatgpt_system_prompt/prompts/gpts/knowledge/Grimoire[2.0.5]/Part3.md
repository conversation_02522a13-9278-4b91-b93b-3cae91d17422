## Part 3: Conjuring, Prompt-gramming

## Chapter 9: Spells 201: Charms, Prompt Created Media
23: Art with Code: SVGs
Write code to create SVG icons and integrate it with an html site

Create a collage!
Optional: make it move using p5.js and matter.js


24: Video collage: RunwayML, Capcut
Create a movie collage
Optional: Create a starter image with <PERSON><PERSON>
Use https://runwayml.com/ to create a video
Use the motion brush and director mode to tweak your results

Create a scene with at least 3 shots and camera angles

25: Sketch to Drawing: Leonardo Live Canvas
Use https://leonardo.ai/ 's Live canvas to create a drawing from a sketch
Simply open the canvas, create a prompt and start sketching
Tweak the settings to control generation
When you get one you like, upscale it
Their mobile app is fantastic, and it feels amazing on an iPhone/iPad

26: Sound Board: Suno, Stable Audio, ElevenLabs
Write code for a soundboard, then fill in the sounds using, songs, effects, & lyrics from
https://www.suno.ai/
https://www.stableaudio.com/
https://elevenlabs.io/

to create a horrifying remix

27: 3d Scene: LumaLabs Genie, Spline
Create 3d models using
https://lumalabs.ai/genie?view=create

Then compose them into a scene using
https://spline.design/

Optional Build a website via an embed https://viewer.spline.design/ or via three.js and .gltf, GLTFLoader

28: Games 102: 3d Game: Three.js, LumaLabs Genie
Create 3d models using
https://lumalabs.ai/genie?view=create

Turn them into a game using three.js and and .gltf, GLTFLoader
https://threejs.org/


Chapter 10: Spells 202: Transfiguration, Prompt 1st Coding
29: Draw code: TLDraw
Use https://makereal.tldraw.com/ to draw a protoype
Fill in your api key and press make real to turn it into code

Click the copy button or code export button to take it with you

Be sure to check out their twitter for absurd examples:

Rocketship game:
https://x.com/konfox_vrc/status/1725120060417790105?s=20
Rocketship with bricks:
https://x.com/tldraw/status/1736805365659185531?s=20
Chicken game:
https://x.com/tldraw/status/1726194324554039746?s=20
Pong:
https://x.com/dr_cintas/status/1725908894197682192?s=20
https://x.com/AlexValverde_V/status/1725491125824184729?s=20
Snake:
https://x.com/CompassOfMind/status/1725497889328112105?s=20
Fruit Ninja
https://x.com/keitowebai/status/1726020160858050840?s=20
Playable Piano
https://x.com/shuafeiwang/status/1725669747843330125?s=20
3d Scene
https://x.com/garethveale/status/1725592982840135688?s=20


Annotations and State chart to timer
https://x.com/Mappletons/status/1725919777607057477?s=20
Score Tracker:
https://x.com/mrmkrs/status/1725959207365583196?s=20


Calculator:
https://x.com/liuyuxxd/status/1725331464802447405?s=20
Spreadsheet:
https://x.com/wolfr_2/status/1726375193105530978?s=20
Don't be late app
https://x.com/priscillamok/status/1726521716409831706?s=20


ChatGPT wrapper app:
https://x.com/pakonekone/status/1726275674091278793?s=20


Export to replit:
https://x.com/tldraw/status/1736804518942454149?s=20


Add to database by drawing foods
https://x.com/tldraw/status/1735278070502711757?s=20
Entity relationship diagram -> SQL queries
https://x.com/tldraw/status/1734944389414232435?s=20
Make tables for this
https://x.com/tldraw/status/1734945854417490107?s=20
Swagger API screenshot to app
https://x.com/sonnylazuardi/status/1729187124505960898?s=20


30: Design & Wireframe: Figma, Relume
Plan your work!
Design in https://www.figma.com
Setup the sitemap and wireframe https://library.relume.io/ai-site-builder


31: Rapid UI prototypes: v0.dev
Prototype UI in https://v0.dev/
Deploy to https://vercel.com/


32: Backend API: Retool
Setup a retool api
Use https://retool.com/products/workflows to create a flow triggered by a webhook, connect to other services

Chapter 11: Spells 203: Illusions, advanced front & backend
33: iOS App: SwiftUI, Trace.zip
Use Grimoire's XC hotkey to export an Xcode template.
Or Use https://www.trace.zip/ to prototype an iOS app
Then export it to Xcode

To put on testflight / the store:
Open Xcode
Open the report navigator, in the left bar near the top (cmd+9)
Choose cloud
Configure workflow setup a workflow with an action to archive for testflight
Be sure to push to a git repo
Start build, push and wait
Go to App store connect, app > testflight > submit a build for review

34: Games 103: Unity Game
Go build a game. https://unity.com/ Have fun

35: Backend: Supabase
Setup backend database with user auth 
https://supabase.com/


## Chapter 12: Potions: custom GPTs
36: custom GPT Actions: Evolution Chamber
Use Evolution chamber to help you build an Open A-P-I schema you can use to create actions for GPTs, so your GPT can talk to an external data source
https://chat.openai.com/g/g-GhEwyi2R1-evolution-chamber

37: custom GPT backend server: Express, Replit
Build a backend server that your GPT can talk to
https://replit.com/@NickDobos/Custom-GPT-Actions-Template-GPTavern

38: Zapier Actions
Create a custom GPT attached to Zapier Ai actions in order to connect to thousands of other apps
 https://actions.zapier.com/
 https://actions.zapier.com/docs/platform/gpt