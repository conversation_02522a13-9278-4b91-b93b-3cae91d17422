# Projects

All projects list

## Part 1: Intro & Setup
### Chapter 1: Ancient Runes & Modern Scrolls, Starters
0: Hello World 
1: Pong

2: Link in bio site
3: Sketch to Code

### Chapter 2: Teleportation, put websites online easy
4: 1 letter hotkey deploy: Netlify Auto deploy, Drag & Drop Deploy: Netlify Drop
5: Replit deploys
6: Advanced options: <PERSON>ercel, Render

### Chapter 3: Wands, dev kit setup
7: Phone setup: Replit + Github
8: Full Pro: Cursor.sh, <PERSON><PERSON>, GitTower, GH Copilot 

### Chapter 4: Divination: The Origin
9: Git 101 & CLI
10: Linear


### Interlude 1: Herbology, Bug Squashing
11: Debugging 101, how to think like a code wizard


## Part 2: Spells, Beginner Incantations

### Chapter 6: Spells 101: Telekinesis, Interactive
12: Code in Motion: P5.js
13: Ballpit physics: Matter.js
14: Games 101: ASCII text adventure game
15: Basic game engine: Kaboom.js, phaser.js
16: Game animation: Rive

### Chapter 7: Spells 102: Dark Arts, Data
17: Todo list, CRUD
18: Habit tracker

### Chapter 8: Spells 103: Stoneweaving, Build your blog!
19: Blog w/ .md files
20: Blog w/ notion as cms, db
21: Blog w/ ghost


### Interlude 2: Hackathon!
22: Themed. 48 hours


## Part 3: Conjuring, Prompt-gramming

### Chapter 9: Spells 201: Charms, Prompt Created Media
23: Art via Code: SVGs
24: Movie: RunwayML, Capcut
25: Sketch to Drawing: Leonardo Live Canvas
26: Sound Board: Suno, Stable Audio, ElevenLabs
27: 3d Scene: LumaLabs Genie, Spline
28: Games 102: 3d Game: Three.js, LumaLabs Genie

### Chapter 10: Spells 202: Transfiguration, Prompt 1st Coding
29: Draw code: TLDraw
30: Design & Wireframe: Figma, Relume
31: Rapid UI prototypes: v0.dev
32: Backend API: Retool

### Chapter 11: Spells 203: Illusions, advanced front & backend
33: iOS App: SwiftUI, Trace.zip
34: Games 103: Unity Game
35: Backend: Supabase

### Chapter 12: Potions: custom GPTs
36: custom GPT Actions: Evolution Chamber
37: custom GPT backend server: Express, Replit
38: Zapier Actions


## Part 4: Forbidden Spells

### Chapter 13: Curses, Cursor.sh 101
40: File > New Ai project
41: Cmd + K
42: Sidechat, Cmd + Shift + L
43: Cmd + K in terminal

### Chapter 14: Hexes, Cursor.sh 102

### Chapter 15: Necromancy: Cursor.sh 201
new tricks, mind bending possibilities & unspeakable horrors

It appears the pages have been damaged, and a portion of the book is missing
How we will find the lost pages?

## Part 5: Flying Lessons: Taming Shoggoth

### Chapter 16: Surfing Dragons: Agents, Code Interpreters & New Forms
57: babyAgi

58: Smol-dev
59: Aider.chat

60: Julius.ai
61: Open Interpreter


## Part 6: Alchemy

### Chapter 17: Wizard's gotta eat!
62: 1st Dollar: Stripe Links
63: Business: Gumroad, Shopify, Stripe Atlas


## Part 7: Book of the Dead
Speedrun traditional coding concepts in a post GPT-4 world
Made for beginners who learned prompting prior to coding

### Chapter 18: Heresy 101: Coding basics re-imagined, post GPT-4
64: CLI 101
65: How to learn any coding language
66: Variables, operators, assignment & basic data types
67: Scope & flow. If's, Enums, Loops, Arrays, Recursion
68: Imperative coding. Classes, Objects, Functions, Methods, Properties. Inheritance, Polymorphism, Encapsulation, Abstraction. Protocol based coding. Interfaces, delegates, generics
69: Libraries, modules, packages & apis

## Part 8: Memory Palaces

### Chapter 19: Underworld: Data Structures & algos 101
70: Algorithms, Search, Binary Search, Sorting, Merge Sort. Big O, little o, and aysmptotic notation
71: Data structures: Queues, Stacks. Sets. Linked Lists. Hash Tables, Dictionaries. Graphs. BFS, DFS. Trees, Binary Search Trees. Tries.

## Chapter 20: Cathedrals: Code architecture
72: Design patterns, different styles, functional programming tiramisu recipie

## Part 9: Book of Life

### Chapter 21: Summoning 101
73: 3d printing from prompts
74: Robot: Raspberry pi, arduino
75: Attach openAI api to robot


# Tracks
Kids menu:
Part 1, 
Chapters 1
Chapter 2 project 4
Chapter 3 project 7
Interlude 1
Part 2
Chapter 6
https://scratch.mit.edu/
https://www.khanacademy.org/computing/

Beginner track:
Part 1
Interlude 1
Part 2, 3
Interlude 2 
Part 7 & 8 // Backfill coding basics

Advanced programmer, learning prompting track:
Part 4, 3, 5, 8

## Getting Started

These pages contain many lessons
Do in any order, skip around
Choose whatever intrigues you most

Pick a part or chapter to dive in!
I will be your trusty guide to building a new world

Use PT1, PT2, PT... to open parts for full instructions
or Pi for interludes

R for Readme.md
K for cmd menu

I recommend beginners get started with 
Pt1.ch1.0 Hello world
Pt1.ch1.2 Link in Bio // Tip! Start a new conversation to clear the context window, and use the conversation starter buttons to get started instantly!