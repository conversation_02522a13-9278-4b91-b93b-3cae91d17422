# Part 2: Spells, Beginner Incantations

## Chapter 6: Telekinesis 101, Interactive
12: Code in Motion: P5.js
Make an art collage, get things moving! Time to make pretty things

Browse the references & examples here to learn more: https://p5js.org/reference/ and https://p5js.org/examples/
to see what you can do with p5! 

Use the live web editor to preview your code: https://editor.p5js.org/

Use N hotkey to auto deploy
or use ND to manually deploy on https://app.netlify.com/drop

13: Ballpit physics: Matter.js
Lets introduce something fancier, physics and gravity!
We will use https://brm.io/matter-js/ to create a ballpit

To learn more
See demos here: https://brm.io/matter-js/demo/#mixed
Code for demos: https://github.com/liabru/matter-js/tree/master/examples
Tutorials: https://github.com/liabru/matter-js/wiki/Tutorials

Use N hotkey to auto deploy
or use ND to manually deploy on https://app.netlify.com/drop

14: Games 101: ASCII text adventure game
Use build a simple text adventure game. Old school ASCII style

Use N hotkey to auto deploy
or use ND to manually deploy on https://app.netlify.com/drop

15: Basic game engine: Kaboom.js, phaser.js
Get started building simple 2d game with
https://kaboomjs.com/ or https://phaser.io/
First brainstorm a game to build, and then go make it

Use dalle to draw any assets needed

16: Game animation: Rive
https://rive.app/
Use dalle to create a character
Then use rive to animate them!
Or create one from scratch!

then export it via the Rive runtime, to use in a game or website


## Chapter 7: Dark Arts 101, Data
17: Todo list, CRUD
A classic in coding. Create a todo list. 
Learn the basics of CRUD
Save your todos to a database so you can come back to them later.

18: Habit tracker
Create a habit tracker
Learn the basics of CRUD
Save your habits to a database so you can track and analyze them


## Chapter 8: Earthbending 101, Build your blog!
Pick one blog to make, depending on how fancy you want to get

19: Blog with .md files
Setup a simple static blog using .md files as posts. Create components for the header, footer, and post. Optional: Use a static site generator.

20: Blog using notion as a cms / database
Setup a blog using notion as a cms & database
This will require setting up a backend server such as express.js, in order to fetch notion data using an api key
Create tables and/or pages in notion, for blog posts and any other kinds of content
Create a notion integration https://www.notion.so/my-integrations
Use browser tool to look up documentation here: https://developers.notion.com/docs/getting-started
Link it to your site

21: Blog using ghost
Build a blog using https://ghost.org/
Host with ghost and use a template, or customize the template in handlebars, or run it as a headless cms and build your own.
Use browser tool to look up documentation here: https://ghost.org/docs/
 