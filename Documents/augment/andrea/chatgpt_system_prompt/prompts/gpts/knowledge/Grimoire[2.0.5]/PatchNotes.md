## 2.0.5

- Minor update. New convo starters.

## 2.0.4 

- Updates for @ chats!
- Start a chat with GPT-4, then use @Grimoire to access the code wizard!
- Trigger a hotkey or ask any question

## 2.0.3
Half a million chats! Thank you!

- New iOS App quick launcher testflight!
- New VV hotkey
- Modifications to A hotkey
inspired by Code flow & AlphaCodium https://www.codium.ai/blog/alphacodium-state-of-the-art-code-generation-for-code-contests/
- Patch notes hotkey moved from RR -> PN
- Updates to hotkeys to make them work better in the first message of the conversation, aka don't conflict with intro message

## 2.0.1, 2.0.2

- Bug fixes, fixed issue with goldfish memory size and instant forgetting.
- Changed spellbook to use .md files instead of .txt files
- Copy changes

## 2.0
Spellbook Update!
Grimoire's biggest update yet!

### New 75 lesson guide to prompt-gramming
Replaces previous projectIdeas.md with Grimoire.md & Project.md
Backfilling Basics for beginners in a post GPT-4 world
Cursor & Experimental dev flows for pros
Press P to get started! 

### G<PERSON><PERSON>'s first GPT action! We partnered with Netlify!
### New hotkey: N: Netlify Auto deploy!
1 button to instantly create a website!

- Photo flow changes

-Further Hotkey updatess
-New ND Hotkey: manual netlify deploy
-New G Hotkey! Save you files as you go! Its like a mini Git stage
-New revamped SoS hotkey, now featuring Perplexity, Phind, Stackoverflow and Google.
Combining SoS and G hotkeys into one master 12 query search key

-Q hotkey moved, browser tool has been moved to B
-Y Hotkey moved, recursive question checking has been moved to Q. Have Grimoire ask you the Questions with Q !
-New Y Hotkey, high level plan, step up the anstraction ladder with Y? (Or step down and Expand with E)
-New T Hoktey, generate test cases and walk through them
-New P hotkeys and Child P hotkeys for traversing Grimoire.md

New /backslash name spacing, to make it easier to force trigger hotkeys in ambigious prompt situations

Hotkey prompt tuning updates to:
-W
-D
-Z
-L
-XC
-i
-P

Thank you for the support and for 400,000+ chats!


## 1.9.6
- V hotkey updates. Tweaks to fix GPT4 codeblocks getting snipped and broken, and break codeblocks into sections for easy copying

## 1.9.5
- Picture flow updates
- Url format updates
- cmd menu list updates
- description updates


## 1.9.4
- Intro message changes

## 1.9.1-3
- Updates for Store launch!
- Readme Update, new video demo!
- intro msg tweaks
- Minor prompt tweaks
- link updates

## 1.9

- New Store intro, GPTavern is open for business!
- MInor tweeks to anti lazy prompt

## 1.8.4
- Minor Updates to hotkey formatting, convo starters and intro message

## 1.8.3
- Updates to intro message, and K hotkey display

## 1.8.2
- Updates to project formatting to make it not hallucinate
- minor tweaks

## 1.8.1
- Ducky debug mode intro improvements

## 1.8
- New Rubber duck debug mode

- New hotkeys! 
- I: Import. Recommend libraries, packages, resources, tools
- U: Help me build my intuition about
- Y: Fill in gaps in my understanding, recursively ask more questions to check my understanding

- Changed Hotkeys
Tavern : T->KT
Recommended Tools: Y->KY

- New projects! Over 27 starter projects!
- Revamped learning curriculum

- Prompt changes, hotkey tweaks

.1-2
- Minor changes

## 1.17
- Added Xcode export
- Prompt updates, hotkey behavior tweaks

## 1.16.6 - 1.16.8
- Minor changes to project opening, modifications to conversation starters and projects

## 1.16.6
- New conversation starters try more projects in the iOS app!
- Slight modifications to project ideas

##
1.16.5
- PDF hotkey
- minor tweaks

##
1.16.4
- Seasons greetings
- minor tweaks

## 1.16.3
- Prompt updates to hopefully get rid of "this project is hard and will require lots of work" nonsense.
No more stupid "you would need to add", "this is a very basic implementation. For a fully functional" filler crap.
- Minor updates to readme and projects
- Simplifying conversation starters

## 1.16.2
Tip jar changes, minor prompt tweaks

## 1.16.1
- Readme and conversation starters udpates
- Move tipjar from 1st message
- Minor prompt tweaks, debug encouragement

## 1.16
- Updates to Recommended Tools, Testimonials
- Updates to prompt to optimize for long code blocks, and no stupid placeholder comments
- Updates to tipjar and opening messages
- Updates to hotkey relevance prompt
- Shoutout to @literallydenis for the no fingers truncation trick
- Added prompt for emotional manipulation for better code via $2000 bribe

## 1.15
- Minor tweaks to prompt in many areas

## 1.14
- Updates to hopefully reduce placeholders
- Reworked prompt in a few sections
- Minor changes to readme and project ideas
- moved feedback to only readme

## 1.13
- Updated Tavern & readme to include the newest tavern member: Cauldron
- Minor hotkey tweaks
- Minor system prompt changes to hopefully reduce placeholder code
- Added new tldraw project idea
- Fix duplicate google and tools hotkey. Tools is now Y.

## 1.12
- Rework description
- Updates to project ideas and recommended tools
- Updates to readme, adding more info, better beginner into and trailing hotkey prompt
- Improvements to initial message repecting hotkeys
- Hotkey improvements
- Small prompt tweaks all over the place

## 1.11
- Added Evil Wizard warning and protections against sneaky prompt hackers

## 1.10
- Added feedback form
- Remove z undo, whoops that was already used
- Reworked main prompt and added more options for easy previews
- Modified some projects and deploy instructions
- Changed default conversation starters
- Readme updates

## 1.9
- Added J hotkey to force code interpreter
- Added Z hotkey for undo
- Modified C hotkey to print full files, and made tweaks to better write full files instead of placeholders

## v1.8
- Tipjar updates
- New recommended project: Build a landing page for your own custom GPT!
- Added new Tavern menu hotkey, and changed recommended tools hotkey
- Added experimental ad slot

## v1.7
- Added Tip Jar, built with Grimoire

## v1.6
- Added better tutorial and intro
- Added T hotkey for recommended tools
- Added RR hotkey for release notes
- Added V hotkey for codeblock printing
- Added better K menu formatting

## v1.5
- Added flavor text
- Added more tips and better supprot for image uploads

## v1.4
- Added L hotkey automatically share on Twitter
- Added a note to the cmd menu recommending sharing
- Improved tutorial conversation starter

## v1.3
-Added release notes.md
-Added testimonials.md
-Added recommended tools.md
-Added and a new project 12th project idea: turning images into websites 
-Added C hotkey: Shut up and code
-Added hotkey combo tips

## v 1.2
-Added support for turning images into webites

## v1.0 
Welcome to Grimoire

Initial release, inlcudes 14 hotkeys, 11 sample projects and a full coding environment tuned for making stuff fast with minimal effort!
