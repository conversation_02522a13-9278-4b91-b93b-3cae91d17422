#!/bin/bash

# <PERSON><PERSON>t to build and run Codex in Docker container
set -e

echo "Building Codex Docker container..."
docker build -f Dockerfile.simple -t codex-simple .

echo "Docker container built successfully!"
echo ""
echo "To run Codex interactively:"
echo "docker run -it --rm -e OPENAI_API_KEY=\$OPENAI_API_KEY -v \$(pwd):/workspace -w /workspace codex-simple codex"
echo ""
echo "To run with a specific command:"
echo "docker run -it --rm -e OPENAI_API_KEY=\$OPENAI_API_KEY -v \$(pwd):/workspace -w /workspace codex-simple codex \"your prompt here\""
echo ""
echo "Make sure to set your OPENAI_API_KEY environment variable first:"
echo "export OPENAI_API_KEY=\"your-api-key-here\""
