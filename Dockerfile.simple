# Simple Dockerfile to install Codex CLI from npm
FROM node:18-slim

# Install basic tools and dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user
RUN useradd -m -s /bin/bash codexuser

# Switch to non-root user
USER codexuser
WORKDIR /home/<USER>

# Install Codex CLI globally
RUN npm install -g @openai/codex

# Set environment variables for safer operation in container
ENV CODEX_UNSAFE_ALLOW_NO_SANDBOX=1

# Default command
CMD ["codex", "--help"]
